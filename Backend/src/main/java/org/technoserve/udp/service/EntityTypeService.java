package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.EnumResponse;
import org.technoserve.udp.entity.dataflow.FileType;
import org.technoserve.udp.entity.valuechain.ValueChain;
import org.technoserve.udp.exception.ResourceNotFoundException;
import org.technoserve.udp.repository.EntityTypeRepository;
import org.technoserve.udp.repository.ValueChainRepository;

import java.util.List;

@Service
@RequiredArgsConstructor
public class EntityTypeService {

  private final EntityTypeRepository entityTypeRepository;

  private final ValueChainRepository valueChainRepository;

  public List<EnumResponse> getEntityTypes(FileType fileType, Long valueChainId) {

    ValueChain valueChain=valueChainRepository.findById(valueChainId)
        .orElseThrow(() -> new ResourceNotFoundException("Value chain not found with ID: " + valueChainId));
    if(fileType.equals(FileType.MASTER)){
      return entityTypeRepository.findByFileTypeAndValueChainType(fileType, null).stream()
          .map(entityType -> new EnumResponse(entityType.getEntityName(), entityType.getDisplayName()))
          .toList();
    }
    return entityTypeRepository.findByFileTypeAndValueChainType(fileType, valueChain.getValueChainType().getType()).stream()
        .map(entityType -> new EnumResponse(entityType.getEntityName(), entityType.getDisplayName()))
        .toList();

  }


}
