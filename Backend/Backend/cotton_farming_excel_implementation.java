    /**
     * Generate cotton farming report as Excel file for download
     * Each row contains farmer details combined with year data - separate row for each farmer-year combination
     *
     * @param partnerId The partner ID to filter by (optional)
     * @param programId The program ID to filter by (optional)
     * @param years The list of years to filter by (required)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateCottonFarmingReportExcel(Long partnerId, Long programId, List<Integer> years, String sortBy, String sortDir) throws IOException {
        // Get all cotton farming data without pagination for Excel export
        List<CottonFarmingReportDto> cottonData = (List<CottonFarmingReportDto>) transactionalReportService.generateCottonFarmingReport(partnerId, programId, years, sortBy, sortDir, 0, Integer.MAX_VALUE).get("content");

        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Cotton Farming Report");

            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // Create header row with combined farmer and cotton farming data columns
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                // Farmer basic information
                "Farmer ID", "Farmer Tracenet Code", "Farmer Name", "Age", "Gender",
                "State", "District", "Village", "Mobile Number", "Centre ID", "Centre Name", "Centre Type",
                "Marital Status", "Spouse Name", "Caste", "Highest Education", "House Hold Size",
                "Land Size Under Cultivation", "Land Measure Type", "Organic Status", "Herd Size",
                "Any Other Income Generating Activity", "Household Annual Income", "Agricultural Annual Income",
                "Dairy Annual Income", "Other Annual Income", "Crops Grown", "Cattle Breed Types",
                "Loan Amount", "Agricultural Loan", "Dairy Loan", "Lat Long", "Partner Name", "Program Name",
                
                // Year-specific cotton farming data
                "Year", "Males In Household", "Females In Household", "Children In Household", "School Going Children", "Earning Members",
                "Total Landholding", "Primary Crop", "Secondary Crops", "Non Organic Cotton Land", "Organic Cotton Land",
                "Years Organic Practice", "Certification Status", "Irrigation Source", "Cattle Count", "Drinking Water Source",
                "Preferred Selling Point", "Has Storage Space", "Receives Agro Advisory", "Received Training",
                "Membership In Org", "Maintains Records", "Annual Household Income (Year)", "Primary Income Source", "Primary Income Amount",
                "Certification Cost Per Acre", "Avg Production Per Acre", "Cost Of Cultivation Per Acre", "Organic Cotton Quantity Sold",
                "Selling Price Per Kg", "Bio Inputs Cost", "Pest Management Bio Inputs", "Bio Fertilizer Used", "Pheromone Traps Per Acre",
                "Harvesting Time", "Weeding Method", "Weeding Cost Per Acre", "Mulching Cost Per Acre", "Tillage Count",
                "Tillage Cost Per Acre", "Land Preparation Cost", "Organic Cotton Seed Rate", "Organic Cotton Seed Variety",
                "Border Crop", "Inter Crop", "Cover Crop", "Trap Crop", "Mulching Used", "Mulching Type", "Storage Precautions",
                "Hired Vehicle For Transport", "Transportation Cost Per Kg", "Rejected Quantity", "Price Discovery Mechanism",
                "Payment Transaction Type", "Credit Days", "Govt Scheme Availed", "Crop Insurance", "Crop Insurance Cost Per Acre",
                "Has KCC", "Has Active Bank Account", "Crop Rotation Used", "Rotation Crops", "Water Tracking Devices",
                "Pump Capacity", "Buffer Zone", "Crop Residue Utilization",
                
                // Calculated fields
                "Total HH Members", "Dependency Ratio", "Gender Ratio", "School Attendance Rate", "Total Cotton Land",
                "Organic Percent", "Land Used For Cotton", "Income Per Earner", "OC Income", "Profit Per Acre",
                "Total Certification Cost", "Total PT Cost", "Total YST Cost", "Total BST Cost", "Total Pest Mgmt Cost",
                "Total Labour Cost", "Machinery Cost Total", "Total Irrigation Cost", "Irrigation Frequency"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows - create separate row for each farmer-year combination
            int rowNum = 1;
            for (CottonFarmingReportDto farmer : cottonData) {
                // If farmer has year data, create a row for each year
                if (farmer.getYearData() != null && !farmer.getYearData().isEmpty()) {
                    for (CottonFarmingYearDataDto yearData : farmer.getYearData()) {
                        Row row = sheet.createRow(rowNum++);
                        populateCombinedFarmerYearRow(row, farmer, yearData, dataStyle);
                    }
                } else {
                    // If no year data, create a row with just farmer data
                    Row row = sheet.createRow(rowNum++);
                    populateCombinedFarmerYearRow(row, farmer, null, dataStyle);
                }
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Populate a row with combined farmer and year data
     */
    private void populateCombinedFarmerYearRow(Row row, CottonFarmingReportDto farmer, CottonFarmingYearDataDto yearData, CellStyle dataStyle) {
        int colIndex = 0;

        // Farmer basic information
        setCellValue(row, colIndex++, farmer.getFarmerId(), dataStyle);
        setCellValue(row, colIndex++, farmer.getFarmerTracenetCode(), dataStyle);
        setCellValue(row, colIndex++, farmer.getFarmerName(), dataStyle);
        setCellValue(row, colIndex++, farmer.getAge(), dataStyle);
        setCellValue(row, colIndex++, farmer.getGender(), dataStyle);
        setCellValue(row, colIndex++, farmer.getState(), dataStyle);
        setCellValue(row, colIndex++, farmer.getDistrict(), dataStyle);
        setCellValue(row, colIndex++, farmer.getVillage(), dataStyle);
        setCellValue(row, colIndex++, farmer.getMobileNumber(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCentreId(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCentreName(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCentreType(), dataStyle);
        setCellValue(row, colIndex++, farmer.getMaritalStatus(), dataStyle);
        setCellValue(row, colIndex++, farmer.getSpouseName(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCaste(), dataStyle);
        setCellValue(row, colIndex++, farmer.getHighestEducation(), dataStyle);
        setCellValue(row, colIndex++, farmer.getHouseHoldSize(), dataStyle);
        setCellValue(row, colIndex++, farmer.getLandSizeUnderCultivation(), dataStyle);
        setCellValue(row, colIndex++, farmer.getLandMeasureType(), dataStyle);
        setCellValue(row, colIndex++, farmer.getOrganicStatus(), dataStyle);
        setCellValue(row, colIndex++, farmer.getHerdSize(), dataStyle);
        setCellValue(row, colIndex++, farmer.getAnyOtherIncomeGeneratingActivity(), dataStyle);
        setCellValue(row, colIndex++, farmer.getHouseholdAnnualIncome(), dataStyle);
        setCellValue(row, colIndex++, farmer.getAgriculturalAnnualIncome(), dataStyle);
        setCellValue(row, colIndex++, farmer.getDairyAnnualIncome(), dataStyle);
        setCellValue(row, colIndex++, farmer.getOtherAnnualIncome(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCropsGrown(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCattleBreedTypes(), dataStyle);
        setCellValue(row, colIndex++, farmer.getLoanAmount(), dataStyle);
        setCellValue(row, colIndex++, farmer.getAgriculturalLoan(), dataStyle);
        setCellValue(row, colIndex++, farmer.getDairyLoan(), dataStyle);
        setCellValue(row, colIndex++, farmer.getLatLong(), dataStyle);
        setCellValue(row, colIndex++, farmer.getPartnerName(), dataStyle);
        setCellValue(row, colIndex++, farmer.getProgramName(), dataStyle);

        // Year-specific cotton farming data
        if (yearData != null) {
            setCellValue(row, colIndex++, yearData.getYear(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMalesInHousehold(), dataStyle);
            setCellValue(row, colIndex++, yearData.getFemalesInHousehold(), dataStyle);
            setCellValue(row, colIndex++, yearData.getChildrenInHousehold(), dataStyle);
            setCellValue(row, colIndex++, yearData.getSchoolGoingChildren(), dataStyle);
            setCellValue(row, colIndex++, yearData.getEarningMembers(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalLandholding(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPrimaryCrop(), dataStyle);
            setCellValue(row, colIndex++, yearData.getSecondaryCrops(), dataStyle);
            setCellValue(row, colIndex++, yearData.getNonOrganicCottonLand(), dataStyle);
            setCellValue(row, colIndex++, yearData.getOrganicCottonLand(), dataStyle);
            setCellValue(row, colIndex++, yearData.getYearsOrganicPractice(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCertificationStatus(), dataStyle);
            setCellValue(row, colIndex++, yearData.getIrrigationSource(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCattleCount(), dataStyle);
            setCellValue(row, colIndex++, yearData.getDrinkingWaterSource(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPreferredSellingPoint(), dataStyle);
            setCellValue(row, colIndex++, yearData.getHasStorageSpace(), dataStyle);
            setCellValue(row, colIndex++, yearData.getReceivesAgroAdvisory(), dataStyle);
            setCellValue(row, colIndex++, yearData.getReceivedTraining(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMembershipInOrg(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMaintainsRecords(), dataStyle);
            setCellValue(row, colIndex++, yearData.getAnnualHouseholdIncome(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPrimaryIncomeSource(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPrimaryIncomeAmount(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCertificationCostPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getAvgProductionPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCostOfCultivationPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getOrganicCottonQuantitySold(), dataStyle);
            setCellValue(row, colIndex++, yearData.getSellingPricePerKg(), dataStyle);
            setCellValue(row, colIndex++, yearData.getBioInputsCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPestManagementBioInputs(), dataStyle);
            setCellValue(row, colIndex++, yearData.getBioFertilizerUsed(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPheromoneTrapsPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getHarvestingTime(), dataStyle);
            setCellValue(row, colIndex++, yearData.getWeedingMethod(), dataStyle);
            setCellValue(row, colIndex++, yearData.getWeedingCostPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMulchingCostPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTillageCount(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTillageCostPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getLandPreparationCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getOrganicCottonSeedRate(), dataStyle);
            setCellValue(row, colIndex++, yearData.getOrganicCottonSeedVariety(), dataStyle);
            setCellValue(row, colIndex++, yearData.getBorderCrop(), dataStyle);
            setCellValue(row, colIndex++, yearData.getInterCrop(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCoverCrop(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTrapCrop(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMulchingUsed(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMulchingType(), dataStyle);
            setCellValue(row, colIndex++, yearData.getStoragePrecautions(), dataStyle);
            setCellValue(row, colIndex++, yearData.getHiredVehicleForTransport(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTransportationCostPerKg(), dataStyle);
            setCellValue(row, colIndex++, yearData.getRejectedQuantity(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPriceDiscoveryMechanism(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPaymentTransactionType(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCreditDays(), dataStyle);
            setCellValue(row, colIndex++, yearData.getGovtSchemeAvailed(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCropInsurance(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCropInsuranceCostPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getHasKCC(), dataStyle);
            setCellValue(row, colIndex++, yearData.getHasActiveBankAccount(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCropRotationUsed(), dataStyle);
            setCellValue(row, colIndex++, yearData.getRotationCrops(), dataStyle);
            setCellValue(row, colIndex++, yearData.getWaterTrackingDevices(), dataStyle);
            setCellValue(row, colIndex++, yearData.getPumpCapacity(), dataStyle);
            setCellValue(row, colIndex++, yearData.getBufferZone(), dataStyle);
            setCellValue(row, colIndex++, yearData.getCropResidueUtilization(), dataStyle);
            
            // Calculated fields
            setCellValue(row, colIndex++, yearData.getTotalHHMembers(), dataStyle);
            setCellValue(row, colIndex++, yearData.getDependencyRatio(), dataStyle);
            setCellValue(row, colIndex++, yearData.getGenderRatio(), dataStyle);
            setCellValue(row, colIndex++, yearData.getSchoolAttendanceRate(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalCottonLand(), dataStyle);
            setCellValue(row, colIndex++, yearData.getOrganicPercent(), dataStyle);
            setCellValue(row, colIndex++, yearData.getLandUsedForCotton(), dataStyle);
            setCellValue(row, colIndex++, yearData.getIncomePerEarner(), dataStyle);
            setCellValue(row, colIndex++, yearData.getOcIncome(), dataStyle);
            setCellValue(row, colIndex++, yearData.getProfitPerAcre(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalCertificationCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalPTCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalYSTCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalBSTCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalPestMgmtCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalLabourCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getMachineryCostTotal(), dataStyle);
            setCellValue(row, colIndex++, yearData.getTotalIrrigationCost(), dataStyle);
            setCellValue(row, colIndex++, yearData.getIrrigationFrequency(), dataStyle);
        } else {
            // If no year data, fill with empty cells for all year-specific columns
            // Count: 1 (year) + 5 (household) + 7 (land/crop) + 5 (infrastructure) + 4 (training) + 3 (income) + 5 (production) + 4 (bio inputs) + 7 (field ops) + 4 (seeds) + 3 (mulching) + 6 (transport) + 4 (govt schemes) + 4 (crop mgmt) + 19 (calculated) = 81 columns
            for (int i = 0; i < 81; i++) {
                setCellValue(row, colIndex++, null, dataStyle);
            }
        }
    }
