// Add this import to the existing imports in ExcelExportService.java:
import java.util.ArrayList;

// Replace the empty generateCottonFarmingReportExcel method with this implementation:

    /**
     * Generate cotton farming report as Excel file for download
     * This creates a flattened Excel with farmer data and all year data in separate columns
     *
     * @param partnerId The partner ID to filter by (optional)
     * @param programId The program ID to filter by (optional)
     * @param years The list of years to filter by (required)
     * @param sortBy The field to sort by
     * @param sortDir The sort direction (asc or desc)
     * @return byte array containing the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateCottonFarmingReportExcel(Long partnerId, Long programId, List<Integer> years, String sortBy, String sortDir) throws IOException {
        // Get all cotton farming data without pagination for Excel export
        List<CottonFarmingReportDto> cottonData = (List<CottonFarmingReportDto>) transactionalReportService.generateCottonFarmingReport(partnerId, programId, years, sortBy, sortDir, 0, Integer.MAX_VALUE).get("content");

        // Create Excel workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Cotton Farming Report");

            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            // Create comprehensive headers for flattened data
            List<String> headersList = createCottonFarmingHeaders(years);
            String[] headers = headersList.toArray(new String[0]);

            // Create header row
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (CottonFarmingReportDto farmer : cottonData) {
                Row row = sheet.createRow(rowNum++);
                populateCottonFarmingRow(row, farmer, years, dataStyle);
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Write to byte array
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }

    /**
     * Create comprehensive headers for cotton farming report including farmer data and year-specific columns
     */
    private List<String> createCottonFarmingHeaders(List<Integer> years) {
        List<String> headers = new ArrayList<>();
        
        // Farmer basic information headers
        headers.add("Farmer ID");
        headers.add("Farmer Tracenet Code");
        headers.add("Farmer Name");
        headers.add("Age");
        headers.add("Gender");
        headers.add("State");
        headers.add("District");
        headers.add("Village");
        headers.add("Mobile Number");
        headers.add("Centre ID");
        headers.add("Centre Name");
        headers.add("Centre Type");
        headers.add("Marital Status");
        headers.add("Spouse Name");
        headers.add("Caste");
        headers.add("Highest Education");
        headers.add("House Hold Size");
        headers.add("Land Size Under Cultivation");
        headers.add("Land Measure Type");
        headers.add("Organic Status");
        headers.add("Herd Size");
        headers.add("Any Other Income Generating Activity");
        headers.add("Household Annual Income");
        headers.add("Agricultural Annual Income");
        headers.add("Dairy Annual Income");
        headers.add("Other Annual Income");
        headers.add("Crops Grown");
        headers.add("Cattle Breed Types");
        headers.add("Loan Amount");
        headers.add("Agricultural Loan");
        headers.add("Dairy Loan");
        headers.add("Lat Long");
        headers.add("Partner Name");
        headers.add("Program Name");

        // Year-specific cotton farming data headers
        for (Integer year : years) {
            String yearPrefix = year + " ";
            
            // Household information
            headers.add(yearPrefix + "Males in Household");
            headers.add(yearPrefix + "Females in Household");
            headers.add(yearPrefix + "Children in Household");
            headers.add(yearPrefix + "School Going Children");
            headers.add(yearPrefix + "Earning Members");
            
            // Land and crop information
            headers.add(yearPrefix + "Total Landholding");
            headers.add(yearPrefix + "Primary Crop");
            headers.add(yearPrefix + "Secondary Crops");
            headers.add(yearPrefix + "Non Organic Cotton Land");
            headers.add(yearPrefix + "Organic Cotton Land");
            headers.add(yearPrefix + "Years Organic Practice");
            headers.add(yearPrefix + "Certification Status");
            headers.add(yearPrefix + "Annual Household Income");
            headers.add(yearPrefix + "Primary Income Source");
            headers.add(yearPrefix + "Primary Income Amount");
            
            // Production and costs
            headers.add(yearPrefix + "Certification Cost Per Acre");
            headers.add(yearPrefix + "Avg Production Per Acre");
            headers.add(yearPrefix + "Cost of Cultivation Per Acre");
            headers.add(yearPrefix + "Organic Cotton Quantity Sold");
            headers.add(yearPrefix + "Selling Price Per Kg");
            headers.add(yearPrefix + "Bio Inputs Cost");
            headers.add(yearPrefix + "Pest Management Bio Inputs");
            headers.add(yearPrefix + "Bio Fertilizer Used");
            headers.add(yearPrefix + "Pheromone Traps Per Acre");
            
            // Field operations
            headers.add(yearPrefix + "Harvesting Time");
            headers.add(yearPrefix + "Weeding Method");
            headers.add(yearPrefix + "Weeding Cost Per Acre");
            headers.add(yearPrefix + "Mulching Cost Per Acre");
            headers.add(yearPrefix + "Tillage Count");
            headers.add(yearPrefix + "Tillage Cost Per Acre");
            headers.add(yearPrefix + "Land Preparation Cost");
            
            // Seeds and crops
            headers.add(yearPrefix + "Organic Cotton Seed Rate");
            headers.add(yearPrefix + "Organic Cotton Seed Variety");
            headers.add(yearPrefix + "Border Crop");
            headers.add(yearPrefix + "Inter Crop");
            headers.add(yearPrefix + "Cover Crop");
            headers.add(yearPrefix + "Trap Crop");
            
            // Mulching and storage
            headers.add(yearPrefix + "Mulching Used");
            headers.add(yearPrefix + "Mulching Type");
            headers.add(yearPrefix + "Storage Precautions");
            
            // Calculated fields
            headers.add(yearPrefix + "Total HH Members");
            headers.add(yearPrefix + "Dependency Ratio");
            headers.add(yearPrefix + "Gender Ratio");
            headers.add(yearPrefix + "School Attendance Rate");
            headers.add(yearPrefix + "Total Cotton Land");
            headers.add(yearPrefix + "Organic Percent");
            headers.add(yearPrefix + "Land Used For Cotton");
            headers.add(yearPrefix + "Income Per Earner");
            headers.add(yearPrefix + "OC Income");
            headers.add(yearPrefix + "Profit Per Acre");
            headers.add(yearPrefix + "Total Certification Cost");
            headers.add(yearPrefix + "Total PT Cost");
            headers.add(yearPrefix + "Total YST Cost");
            headers.add(yearPrefix + "Total BST Cost");
            headers.add(yearPrefix + "Total Pest Mgmt Cost");
            headers.add(yearPrefix + "Total Labour Cost");
            headers.add(yearPrefix + "Machinery Cost Total");
            headers.add(yearPrefix + "Total Irrigation Cost");
            headers.add(yearPrefix + "Irrigation Frequency");
        }
        
        return headers;
    }

    /**
     * Populate a row with cotton farming data for a specific farmer
     */
    private void populateCottonFarmingRow(Row row, CottonFarmingReportDto farmer, List<Integer> years, CellStyle dataStyle) {
        int colIndex = 0;
        
        // Farmer basic information
        setCellValue(row, colIndex++, farmer.getFarmerId(), dataStyle);
        setCellValue(row, colIndex++, farmer.getFarmerTracenetCode(), dataStyle);
        setCellValue(row, colIndex++, farmer.getFarmerName(), dataStyle);
        setCellValue(row, colIndex++, farmer.getAge(), dataStyle);
        setCellValue(row, colIndex++, farmer.getGender(), dataStyle);
        setCellValue(row, colIndex++, farmer.getState(), dataStyle);
        setCellValue(row, colIndex++, farmer.getDistrict(), dataStyle);
        setCellValue(row, colIndex++, farmer.getVillage(), dataStyle);
        setCellValue(row, colIndex++, farmer.getMobileNumber(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCentreId(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCentreName(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCentreType(), dataStyle);
        setCellValue(row, colIndex++, farmer.getMaritalStatus(), dataStyle);
        setCellValue(row, colIndex++, farmer.getSpouseName(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCaste(), dataStyle);
        setCellValue(row, colIndex++, farmer.getHighestEducation(), dataStyle);
        setCellValue(row, colIndex++, farmer.getHouseHoldSize(), dataStyle);
        setCellValue(row, colIndex++, farmer.getLandSizeUnderCultivation(), dataStyle);
        setCellValue(row, colIndex++, farmer.getLandMeasureType(), dataStyle);
        setCellValue(row, colIndex++, farmer.getOrganicStatus(), dataStyle);
        setCellValue(row, colIndex++, farmer.getHerdSize(), dataStyle);
        setCellValue(row, colIndex++, farmer.getAnyOtherIncomeGeneratingActivity(), dataStyle);
        setCellValue(row, colIndex++, farmer.getHouseholdAnnualIncome(), dataStyle);
        setCellValue(row, colIndex++, farmer.getAgriculturalAnnualIncome(), dataStyle);
        setCellValue(row, colIndex++, farmer.getDairyAnnualIncome(), dataStyle);
        setCellValue(row, colIndex++, farmer.getOtherAnnualIncome(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCropsGrown(), dataStyle);
        setCellValue(row, colIndex++, farmer.getCattleBreedTypes(), dataStyle);
        setCellValue(row, colIndex++, farmer.getLoanAmount(), dataStyle);
        setCellValue(row, colIndex++, farmer.getAgriculturalLoan(), dataStyle);
        setCellValue(row, colIndex++, farmer.getDairyLoan(), dataStyle);
        setCellValue(row, colIndex++, farmer.getLatLong(), dataStyle);
        setCellValue(row, colIndex++, farmer.getPartnerName(), dataStyle);
        setCellValue(row, colIndex++, farmer.getProgramName(), dataStyle);

        // Year-specific cotton farming data
        for (Integer year : years) {
            // Find year data for this specific year
            CottonFarmingYearDataDto yearData = farmer.getYearData().stream()
                .filter(yd -> yd.getYear().equals(year))
                .findFirst()
                .orElse(null);
            
            if (yearData != null) {
                // Household information
                setCellValue(row, colIndex++, yearData.getMalesInHousehold(), dataStyle);
                setCellValue(row, colIndex++, yearData.getFemalesInHousehold(), dataStyle);
                setCellValue(row, colIndex++, yearData.getChildrenInHousehold(), dataStyle);
                setCellValue(row, colIndex++, yearData.getSchoolGoingChildren(), dataStyle);
                setCellValue(row, colIndex++, yearData.getEarningMembers(), dataStyle);
                
                // Land and crop information
                setCellValue(row, colIndex++, yearData.getTotalLandholding(), dataStyle);
                setCellValue(row, colIndex++, yearData.getPrimaryCrop(), dataStyle);
                setCellValue(row, colIndex++, yearData.getSecondaryCrops(), dataStyle);
                setCellValue(row, colIndex++, yearData.getNonOrganicCottonLand(), dataStyle);
                setCellValue(row, colIndex++, yearData.getOrganicCottonLand(), dataStyle);
                setCellValue(row, colIndex++, yearData.getYearsOrganicPractice(), dataStyle);
                setCellValue(row, colIndex++, yearData.getCertificationStatus(), dataStyle);
                setCellValue(row, colIndex++, yearData.getAnnualHouseholdIncome(), dataStyle);
                setCellValue(row, colIndex++, yearData.getPrimaryIncomeSource(), dataStyle);
                setCellValue(row, colIndex++, yearData.getPrimaryIncomeAmount(), dataStyle);
                
                // Production and costs
                setCellValue(row, colIndex++, yearData.getCertificationCostPerAcre(), dataStyle);
                setCellValue(row, colIndex++, yearData.getAvgProductionPerAcre(), dataStyle);
                setCellValue(row, colIndex++, yearData.getCostOfCultivationPerAcre(), dataStyle);
                setCellValue(row, colIndex++, yearData.getOrganicCottonQuantitySold(), dataStyle);
                setCellValue(row, colIndex++, yearData.getSellingPricePerKg(), dataStyle);
                setCellValue(row, colIndex++, yearData.getBioInputsCost(), dataStyle);
                setCellValue(row, colIndex++, yearData.getPestManagementBioInputs(), dataStyle);
                setCellValue(row, colIndex++, yearData.getBioFertilizerUsed(), dataStyle);
                setCellValue(row, colIndex++, yearData.getPheromoneTrapsPerAcre(), dataStyle);
                
                // Field operations
                setCellValue(row, colIndex++, yearData.getHarvestingTime(), dataStyle);
                setCellValue(row, colIndex++, yearData.getWeedingMethod(), dataStyle);
                setCellValue(row, colIndex++, yearData.getWeedingCostPerAcre(), dataStyle);
                setCellValue(row, colIndex++, yearData.getMulchingCostPerAcre(), dataStyle);
                setCellValue(row, colIndex++, yearData.getTillageCount(), dataStyle);
                setCellValue(row, colIndex++, yearData.getTillageCostPerAcre(), dataStyle);
                setCellValue(row, colIndex++, yearData.getLandPreparationCost(), dataStyle);
                
                // Seeds and crops
                setCellValue(row, colIndex++, yearData.getOrganicCottonSeedRate(), dataStyle);
                setCellValue(row, colIndex++, yearData.getOrganicCottonSeedVariety(), dataStyle);
                setCellValue(row, colIndex++, yearData.getBorderCrop(), dataStyle);
                setCellValue(row, colIndex++, yearData.getInterCrop(), dataStyle);
                setCellValue(row, colIndex++, yearData.getCoverCrop(), dataStyle);
                setCellValue(row, colIndex++, yearData.getTrapCrop(), dataStyle);
                
                // Mulching and storage
                setCellValue(row, colIndex++, yearData.getMulchingUsed(), dataStyle);
                setCellValue(row, colIndex++, yearData.getMulchingType(), dataStyle);
                setCellValue(row, colIndex++, yearData.getStoragePrecautions(), dataStyle);
                
                // Calculated fields
                setCellValue(row, colIndex++, yearData.getTotalHHMembers(), dataStyle);
                setCellValue(row, colIndex++, yearData.getDependencyRatio(), dataStyle);
                setCellValue(row, colIndex++, yearData.getGenderRatio(), dataStyle);
                setCellValue(row, colIndex++, yearData.getSchoolAttendanceRate(), dataStyle);
                setCellValue(row, colIndex++, yearData.getTotalCottonLand(), dataStyle);
                setCellValue(row, colIndex++, yearData.getOrganicPercent(), dataStyle);
                setCellValue(row, colIndex++, yearData.getLandUsedForCotton(), dataStyle);
                setCellValue(row, colIndex++, yearData.getIncomePerEarner(), dataStyle);
                setCellValue(row, colIndex++, yearData.getOcIncome(), dataStyle);
                setCellValue(row, colIndex++, yearData.getProfitPerAcre(), dataStyle);
                setCellValue(row, colIndex++, yearData.getTotalCertificationCost(), dataStyle);
                setCellValue(row, colIndex++, yearData.getTotalPTCost(), dataStyle);
                setCellValue(row, colIndex++, yearData.getTotalYSTCost(), dataStyle);
                setCellValue(row, colIndex++, yearData.getTotalBSTCost(), dataStyle);
                setCellValue(row, colIndex++, yearData.getTotalPestMgmtCost(), dataStyle);
                setCellValue(row, colIndex++, yearData.getTotalLabourCost(), dataStyle);
                setCellValue(row, colIndex++, yearData.getMachineryCostTotal(), dataStyle);
                setCellValue(row, colIndex++, yearData.getTotalIrrigationCost(), dataStyle);
                setCellValue(row, colIndex++, yearData.getIrrigationFrequency(), dataStyle);
            } else {
                // If no data for this year, add empty cells for all year-specific columns
                // Count: 5 + 10 + 9 + 6 + 3 + 19 = 52 columns per year
                for (int i = 0; i < 52; i++) {
                    setCellValue(row, colIndex++, null, dataStyle);
                }
            }
        }
    }
